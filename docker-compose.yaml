version: '3.9'

services:
  pilardin:
    build:
      context: .
      args:
        OLLAMA_BASE_URL: '/ollama'
      dockerfile: Dockerfile
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    container_name: Pilardin.RAG
    volumes:
      - Pilardin.RAG:/app/backend/data
    ports:
      - ${PILARDIN_PORT-4000}:8090
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434      # Internal service DNS name
      - TIKA_SERVER_URL=http://tika:9998         # Use service name, not container name
      - PILARDIN_SECRET_KEY=${PILARDIN_SECRET_KEY:-}
    depends_on:
      - ollama
      - tika
    restart: unless-stopped
    networks:
      - pilardin-net

  ollama:
    image: ollama/ollama
    container_name: Pilardin.Ollama
    ports:
      - 11434:11434
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    networks:
      - pilardin-net

  tika:
    build:
      context: .
      dockerfile: Dockerfile.tika
    container_name: Pilardin.Tika
    ports:
      - 9998:9998
    environment:
      - TIKA_SERVER_ENDPOINT=/
      - TIKA_CONFIG_PATH=/tmp/tika-config.xml
      - JAVA_OPTS=-Xmx2g -Xms512m -Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom
    volumes:
      - tika_tmp:/tmp/tika-temp
      - tika_logs:/tmp/tika-logs
    restart: unless-stopped
    networks:
      - pilardin-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9998/tika"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  Pilardin.RAG:
    external: true
  ollama_data:
    driver: local
  tika_tmp:
    driver: local
  tika_logs:
    driver: local

networks:
  pilardin-net:
    driver: bridge
